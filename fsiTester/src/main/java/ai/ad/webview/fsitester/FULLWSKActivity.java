package ai.ad.webview.fsitester;

import android.app.Activity;
import android.app.KeyguardManager;
import android.content.Context;
import android.content.Intent;
import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import android.os.Build;
import android.os.Bundle;
import android.util.Log;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.webkit.WebChromeClient;
import android.webkit.WebResourceRequest;
import android.webkit.WebSettings;
import android.webkit.WebView;
import android.webkit.WebViewClient;
import android.widget.Button;
import android.widget.ImageButton;
import android.widget.TextView;

/**
 * 全屏FSI Activity
 * 在FSI通知点击后显示全屏内容
 */
public class FULLWSKActivity extends Activity {
    private static final String TAG = "FULLWSKActivity";
    private static final String BAIDU_URL = "https://www.baidu.com";

    private TextView tvTitle;
    private TextView tvContent;
    private Button btnClose;
    private ImageButton ibClose;
    private WebView webView;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        WSKLog.d(TAG, "onCreate -> 初始化全屏FSI Activity");

        // 设置锁屏显示和屏幕唤醒
        turnScreenOnAndKeyguardOff();

        // 配置窗口为透明
        configureWindow(getWindow());

        // 设置窗口特性
        setupWindow();

        // 设置内容视图
        setContentView(R.layout.activity_fullwsk);

        // 初始化视图
        initViews();

        // 设置事件监听器
        setupListeners();

        // 设置WebView
        setupWebView();

        // 处理Intent数据
        handleIntentData();

        // 通知FSI管理器Activity已显示
        FSIManager.getInstance().updateShowingState(true);
    }

    /**
     * 配置窗口为透明
     *
     * @param window 窗口对象
     */
    public void configureWindow(Window window) {
        WSKLog.d(TAG, "configureWindow -> Starting window configuration");
        if (window == null) {
            WSKLog.e(TAG, "configureWindow -> Cannot configure window: window is null");
            return;
        }

        try {
            // 设置窗口背景为透明
            window.setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));
            window.setBackgroundDrawableResource(android.R.color.transparent);

            // 设置窗口布局参数
            WindowManager.LayoutParams params = window.getAttributes();
            params.width = WindowManager.LayoutParams.MATCH_PARENT;
            params.height = WindowManager.LayoutParams.MATCH_PARENT;

            // 设置状态栏和导航栏透明（Android 5.0及以上）
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
                window.addFlags(WindowManager.LayoutParams.FLAG_DRAWS_SYSTEM_BAR_BACKGROUNDS);
                window.clearFlags(WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS);
                window.setStatusBarColor(Color.TRANSPARENT); // 设置状态栏透明
                window.setNavigationBarColor(Color.TRANSPARENT); // 设置导航栏透明
            }

            WSKLog.d(TAG, "configureWindow -> Window configuration completed");
        } catch (Exception e) {
            WSKLog.e(TAG, "configureWindow -> Error configuring window: " + e.getMessage());
        }
    }

    /**
     * 设置窗口特性
     */
    private void setupWindow() {
        Window window = getWindow();

        // 保持屏幕常亮
        window.addFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON);

        // 在锁屏上显示
        window.addFlags(WindowManager.LayoutParams.FLAG_SHOW_WHEN_LOCKED
                | WindowManager.LayoutParams.FLAG_TURN_SCREEN_ON
                | WindowManager.LayoutParams.FLAG_DISMISS_KEYGUARD);

        // 添加更强的窗口标志，确保Activity能在前台显示
        window.addFlags(WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE
                | WindowManager.LayoutParams.FLAG_NOT_TOUCH_MODAL
                | WindowManager.LayoutParams.FLAG_WATCH_OUTSIDE_TOUCH);

        // 确保Activity显示在最顶层
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            window.setType(WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY);
        } else {
            window.setType(WindowManager.LayoutParams.TYPE_SYSTEM_ALERT);
        }

        // 适配刘海屏
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.P) {
            window.getAttributes().layoutInDisplayCutoutMode =
                    WindowManager.LayoutParams.LAYOUT_IN_DISPLAY_CUTOUT_MODE_SHORT_EDGES;
        }

        // 适配沉浸式状态栏
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT) {
            window.getDecorView().setSystemUiVisibility(
                    View.SYSTEM_UI_FLAG_LAYOUT_STABLE
                    | View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
                    | View.SYSTEM_UI_FLAG_FULLSCREEN
                    | View.SYSTEM_UI_FLAG_IMMERSIVE_STICKY);
        }

        WSKLog.d(TAG, "setupWindow -> 窗口特性设置完成");
    }

    /**
     * 初始化视图
     */
    private void initViews() {
        tvTitle = findViewById(R.id.tvTitle);
        tvContent = findViewById(R.id.tvContent);
        btnClose = findViewById(R.id.btnClose);
        ibClose = findViewById(R.id.ibClose);
        webView = findViewById(R.id.webView);

        WSKLog.d(TAG, "initViews -> 视图初始化完成");
    }

    /**
     * 设置WebView
     */
    private void setupWebView() {
        if (webView == null) {
            WSKLog.e(TAG, "setupWebView -> WebView为空");
            return;
        }

        // 配置WebView
        WebSettings settings = webView.getSettings();
        settings.setJavaScriptEnabled(true);
        settings.setDomStorageEnabled(true);
        settings.setUseWideViewPort(true);
        settings.setLoadWithOverviewMode(true);

        // 设置WebViewClient避免在外部浏览器中打开
        webView.setWebViewClient(new WebViewClient() {
            @Override
            public boolean shouldOverrideUrlLoading(WebView view, WebResourceRequest request) {
                return false; // 返回false表示在WebView中处理URL
            }

            @Override
            public void onPageFinished(WebView view, String url) {
                super.onPageFinished(view, url);
                WSKLog.d(TAG, "onPageFinished -> 页面加载完成: " + url);
            }
        });

        // 设置WebChromeClient处理JavaScript对话框等
        webView.setWebChromeClient(new WebChromeClient());

        // 加载百度首页
        webView.loadUrl(BAIDU_URL);
        WSKLog.d(TAG, "setupWebView -> 开始加载百度首页");
    }

    /**
     * 设置事件监听器
     */
    private void setupListeners() {
        // 设置关闭按钮点击事件
        btnClose.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                closeActivity();
            }
        });

        // 设置右上角关闭按钮点击事件
        ibClose.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                closeActivity();
            }
        });

        WSKLog.d(TAG, "setupListeners -> 事件监听器设置完成");
    }

    /**
     * 处理Intent数据
     */
    private void handleIntentData() {
        Intent intent = getIntent();
        if (intent != null) {
            String title = intent.getStringExtra("title");
            String content = intent.getStringExtra("content");

            if (title != null && !title.isEmpty()) {
                tvTitle.setText(title);
            }

            if (content != null && !content.isEmpty()) {
                tvContent.setText(content);
            }

            WSKLog.d(TAG, "handleIntentData -> Intent数据处理完成");
        }
    }

    /**
     * 关闭Activity
     */
    private void closeActivity() {
        WSKLog.d(TAG, "closeActivity -> 关闭全屏FSI Activity");
        finish();
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();

        // 销毁WebView
        if (webView != null) {
            webView.stopLoading();
            webView.destroy();
            webView = null;
        }

        // 通知FSI管理器Activity已关闭
        FSIManager.getInstance().updateShowingState(false);

        // 取消通知
        FSIManager.getInstance().closeFSI();

        WSKLog.d(TAG, "onDestroy -> 全屏FSI Activity已销毁");
    }

    @Override
    public void onBackPressed() {
        // 处理WebView返回
        if (webView != null && webView.canGoBack()) {
            webView.goBack();
        } else {
            // 如果WebView无法返回，则关闭Activity
            closeActivity();
        }
    }

    /**
     * 设置屏幕唤醒和锁屏显示
     * 基于参考项目的最佳实践实现
     */
    private void turnScreenOnAndKeyguardOff() {
        WSKLog.d(TAG, "turnScreenOnAndKeyguardOff -> 设置屏幕唤醒和锁屏显示");

        try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O_MR1) {
                // Android 8.1及以上版本
                setShowWhenLocked(true);
                setTurnScreenOn(true);
                WSKLog.d(TAG, "turnScreenOnAndKeyguardOff -> 使用新API设置锁屏显示");
            } else {
                // Android 8.1以下版本
                getWindow().addFlags(
                        WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON
                        | WindowManager.LayoutParams.FLAG_ALLOW_LOCK_WHILE_SCREEN_ON
                        | WindowManager.LayoutParams.FLAG_SHOW_WHEN_LOCKED
                        | WindowManager.LayoutParams.FLAG_TURN_SCREEN_ON);
                WSKLog.d(TAG, "turnScreenOnAndKeyguardOff -> 使用旧API设置锁屏显示");
            }

            // 请求解除锁屏
            KeyguardManager keyguardManager = (KeyguardManager) getSystemService(Context.KEYGUARD_SERVICE);
            if (keyguardManager != null && Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                keyguardManager.requestDismissKeyguard(this, null);
                WSKLog.d(TAG, "turnScreenOnAndKeyguardOff -> 请求解除锁屏");
            }
        } catch (Exception e) {
            WSKLog.e(TAG, "turnScreenOnAndKeyguardOff -> 设置失败: " + e.getMessage());
        }
    }
}